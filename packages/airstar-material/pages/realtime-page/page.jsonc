{"type": "page", "body": [{"type": "tpl", "tpl": "<div>kk1 ##{a}</div>"}, {"type": "polaris-service-insight-content-realtime", "id": "polaris-service-insight-content-realtime", "filters": [{"type": "flatCascader", "label": "作者管理类目a", "name": "channelIds", "key": "channelIds", "multiple": true, "allValue": -1, "options": "${channelIds.items}", "labelStyle": {"font-size": "14px", "line-height": "22px"}}, {"key": "hetuCategory", "name": "hetuCategory", "type": "flatCascader", "multiple": true, "options": "${hetuCategory.items}", "label": "视频河图类目", "allValue": -1, "labelStyle": {"font-size": "14px", "line-height": "22px"}}, {"key": "isHotView", "type": "flatMixin", "label": "高级筛选", "mixinLabel": "热点相关", "needMore": true, "mixinType": "select", "options": "$isHotView", "isRadio": true, "notAutoSelectFirstChild": true, "multiple": true, "labelStyle": {"font-size": "14px", "line-height": "22px"}}, {"key": "isVideo", "type": "flatMixin", "mixinLabel": "作者类型", "needMore": true, "mixinType": "select", "options": "$isVideo", "isRadio": true, "notAutoSelectFirstChild": true, "multiple": true}], "filterApi": {"method": "post", "url": "/rest/low-code/handle/kuaishou-operation-super-hero/aitest/xty/getFilterOptions", "adaptor": "console.log('[debug]handle filter options return data, response? processedData', response);\n\nfunction replaceChildListWithChildren(obj) {\n    if (Array.isArray(obj)) {\n        return obj.map(item => replaceChildListWithChildren(item));\n    } else if (obj && typeof obj === 'object') {\n        const newObj = {};\n        for (const [key, value] of Object.entries(obj)) {\n            if (key === 'childList') {\n                newObj['children'] = replaceChildListWithChildren(value);\n            } else {\n                newObj[key] = replaceChildListWithChildren(value);\n            }\n        }\n        return newObj;\n    }\n    return obj;\n}\n\n// 获取原始数据\nconst originalData = response.data.data || {};\n\n// 处理数据\nconst processedData = replaceChildListWithChildren(originalData);\n\nconsole.log(processedData, 'mlh processedData')\n\nreturn {\n    ...response?.data,\n    data: processedData,\n    status: response.status === 200 ? 0 : response.status,\n    msg: response.message || '服务端异常'\n};"}, "searchApi": {"url": "/rest/insight/actual/photo", "method": "post", "data": {"authorInfo": "${authorInfo}", "authorWhiteList": [], "categoryIds": [], "compare": "0", "&": "${filterData}", "authorKey": "$authorKey", "deepAfid": "", "hashTagList": [], "hetuTagIds": [], "mediumPhoto": -1, "needClusterInfo": false, "page": "${pageData.page}", "photoHasRelated": -1, "photoInfo": "${photoInfo}", "photoPushTime": [], "photoUseType": -1, "relatedDeepAfidPhotoId": "", "theme": 1, "type": "1", "verifiedTypeList": [], "count": "${pageData.count}"}, "adaptor": "console.log('[debug]handle service list return data, response?', response);\nreturn {\n    ...response?.data,\n    data: {\n        ...response?.data.data,\n    },\n    status: response.status === 200 ? 0 : response.status,\n    msg: response.message || '服务端异常'\n}"}, "downloadApi": {"method": "post", "url": "/rest/insight/actual/photo/export", "data": {"&": "${filterData}", "authorKey": "$authorKey", "orderBy": "${orderBy|default:undefined}", "page": "${pageData.page}", "count": "${pageData.count}"}}, "filterData": {"channelIds": [[-1]], "hetuCategory": [[-1]], "page": 1, "count": 1}, "initFilterData": {"channelIds": [[-1]], "hetuCategory": [[-1]], "page": 1, "count": 1}, "columns": [{"prop": "selection", "key": "selection", "attrs": {"width": "55px", "type": "selection"}}, {"label": "排名", "key": "rank", "prop": "rank", "slot": {"type": "polaris-middleware", "middleware": "rank", "name": "rank", "props": {"row": {"rank": "$row.rank"}}}, "attrs": {"width": "80px", "fixed": "left"}}, {"label": "视频", "key": "photoVideo", "prop": "photoVideo", "slot": {"type": "polaris-middleware", "middleWare": "photo-data", "props": {"photoData": {"photo": {"photoId": "$row.photoId", "caption": "$row.photoInfo.caption", "hetuTags": "情感/情感关系", "publishTime": "$row.photoInfo.timeMillis", "thumbUrl": "$row.photoInfo.thumbUrl"}, "user": {"userId": "$row.userId", "userName": "$row.userName", "userAvatar": "$row.userAvatarUrl", "isVerified": true, "fansCount": "$row.fansCount", "category": "PR媒体", "verifiedTypeDesc": "$row.verifiedTypeDesc", "verifiedDesc": "$row.verifiedDesc"}}, "layoutMode": "standard", "userJump": true, "notPlay": false, "titleLayout": ["title", "label"], "userIdCopy": true, "userIdJump": true}}, "attrs": {"min-width": "500px", "width": "500px", "fixed": "left"}}, {"label": "有效播放次数", "key": "playCount", "prop": "playCount", "slot": {"type": "polaris-middleware", "middleWare": "multiple-number-display", "name": "playCount", "props": {"dataList": [{"key": "totalAmount", "unit": "W", "label": "+ "}, {"key": "changeAmount", "unit": "W", "label": "累计 "}], "multipleData": {"totalAmount": "$row.playCount", "changeAmount": "$row.photoInfo.photoCountInfo.viewCount"}, "wUnitSuffixIsChinese": true, "hideLabelWhenNull": false, "defaultValue": "--"}}, "attrs": {"min-width": "150px"}}, {"label": "点赞次数", "key": "likeCount", "prop": "item", "slot": {"type": "polaris-middleware", "middleWare": "multiple-number-display", "props": {"dataList": [{"key": "totalAmount", "unit": "W", "label": "+ "}, {"key": "changeAmount", "unit": "W", "label": "累计 "}], "multipleData": {"totalAmount": "$row.likeCount", "changeAmount": "$row.photoInfo.photoCountInfo.likeCount"}, "wUnitSuffixIsChinese": true, "hideLabelWhenNull": false, "defaultValue": "--"}}, "attrs": {"min-width": "150px"}}, {"label": "评论次数", "key": "commentCount", "prop": "item", "slot": {"type": "polaris-middleware", "middleWare": "multiple-number-display", "props": {"dataList": [{"key": "totalAmount", "unit": "W", "label": "+ "}, {"key": "changeAmount", "unit": "W", "label": "累计 "}], "multipleData": {"totalAmount": "$row.commentCount", "changeAmount": "$row.photoInfo.photoCountInfo.commentCount"}, "wUnitSuffixIsChinese": true, "hideLabelWhenNull": false, "defaultValue": "--"}}, "attrs": {"min-width": "150px"}}, {"label": "分享次数", "key": "shareCount", "prop": "item", "headerSlot": {"framework": "vue", "type": "common-header-tooltip", "props": {"title": "分享次数", "tooltip": "统计视频0点至当前真实分享次数（限定分享成功）", "effect": "dark", "placement": "top", "underline": true, "titleClassName": "column-underline"}}, "slot": {"type": "polaris-middleware", "middleWare": "multiple-number-display", "props": {"dataList": [{"key": "totalAmount", "unit": "W", "label": "+ "}, {"key": "changeAmount", "unit": "W", "label": "累计 "}], "multipleData": {"totalAmount": "$row.shareCount", "changeAmount": "$row.photoShareCount"}, "wUnitSuffixIsChinese": true, "hideLabelWhenNull": false, "defaultValue": "--"}}, "attrs": {"min-width": "150px"}}, {"label": "收藏次数", "key": "storeCount", "prop": "item", "slot": {"type": "polaris-middleware", "middleWare": "multiple-number-display", "props": {"dataList": [{"key": "totalAmount", "unit": "W", "label": "+ "}, {"key": "changeAmount", "unit": "W", "label": "累计 "}], "multipleData": {"totalAmount": "$row.storeCount", "changeAmount": "$row.photoCollectCount"}, "wUnitSuffixIsChinese": true, "hideLabelWhenNull": false, "defaultValue": "--"}}, "attrs": {"min-width": "200px"}}, {"label": "操作", "key": "operation", "prop": "operation", "slot": {"type": "wrapper", "framework": "react", "body": [{"type": "wrapper", "body": [{"type": "polaris-middleware", "middleWare": "polaris-button", "id": "video-detail-button", "props": {"label": "视频详情", "type": "text", "style": {"color": "#326bfb"}, "wrapperClassName": "button-row", "wrapperStyle": {"marginBottom": "8px"}, "eventName": "click"}, "onEvent": {"click": {"actions": [{"actionType": "custom", "script": "console.log('已点击视频详情')"}]}}}, {"type": "tpl", "tpl": "<div class='vertical-line'></div>", "inline": true, "style": {"marginLeft": "8px", "marginRight": "8px", "borderLeft": "1px solid #EBEDF0", "height": "10px", "display": "inline-block"}}, {"type": "polaris-middleware", "middleWare": "polaris-button", "id": "report-hotspot-button", "props": {"label": "提报热点", "type": "text", "style": {"color": "#326bfb", "marginRight": "5.5px"}, "eventName": "click"}, "onEvent": {"click": {"actions": [{"actionType": "custom", "script": "console.log('已点击提报热点')"}]}}}]}, {"type": "wrapper", "body": [{"type": "polaris-middleware", "middleWare": "polaris-button", "id": "distribution-detail-button", "props": {"label": "分发详情", "type": "text", "style": {"color": "#326bfb"}, "wrapperClassName": "button-row", "wrapperStyle": {"marginBottom": "8px"}, "eventName": "click"}, "onEvent": {"click": {"actions": [{"actionType": "custom", "script": "console.log('已点击分发详情')"}]}}}, {"type": "tpl", "tpl": "<div class='vertical-line'></div>", "inline": true, "style": {"marginLeft": "8px", "marginRight": "8px", "borderLeft": "1px solid #EBEDF0", "height": "10px", "display": "inline-block"}}, {"type": "polaris-middleware", "middleWare": "polaris-button", "id": "forward-button", "props": {"label": "转发", "type": "text", "style": {"color": "#326bfb", "marginRight": "5.5px"}, "eventName": "click"}, "onEvent": {"click": {"actions": [{"actionType": "custom", "script": "console.log('已点击转发')"}]}}}]}, {"type": "wrapper", "body": [{"type": "polaris-middleware", "middleWare": "polaris-button", "id": "placement-button", "props": {"label": "投放", "type": "text", "style": {"color": "#326bfb", "marginRight": "5.5px"}, "eventName": "click"}, "onEvent": {"click": {"actions": [{"actionType": "custom", "script": "console.log('已点击投放')"}]}}}]}]}, "attrs": {"min-width": "100px", "width": "160px", "fixed": "right", "class-name": "table-operation"}}], "body": [{"type": "flex", "justify": "flex-start", "alignItems": "center", "items": [{"type": "polaris-middleware", "id": "common-input", "middleware": "common-input", "name": "<PERSON><PERSON><PERSON>", "data": {"modelValue": "$authorKey", "placeholder": "输入视频 id/标题进行搜索", "append": false, "showInnerIcon": true, "showClearable": true, "style": {"width": "480px"}}, "onEvent": {"clear": {"actions": [{"actionType": "custom", "script": "console.log('清除按钮被点击')"}]}, "search": {"actions": [{"actionType": "custom", "script": "console.log('搜索事件触发', event.data)"}]}}}], "style": {"marginTop": "24px", "marginBottom": "24px", "marginLeft": "5px"}}, {"type": "polaris-complex-filter-v1", "id": "polaris-complex-filter-v1", "labelAlign": "left", "operationVisible": false, "name": "filterData", "left": 128, "style": {"padding": "10px", "background": "#fff", "borderRadius": "0 0 8px 8px", "marginBottom": "24px"}, "data": {"filters": "$filters", "filterData": "$filterData", "initFilterData": "$initFilterData"}}, {"type": "polaris-ks-table", "id": "polaris-ks-table", "submitOnChange": true, "headerToolbar": [{"type": "flex", "className": "mb_10  !jc_space_between", "alignItems": "center", "items": [{"type": "wrapper", "className": "flex ai_center", "body": [{"type": "tpl", "tpl": "<div class='ts_14 tc_898a8c mr_16 flex speator-line'>共<span class='tc_252626 pl_2 pr_2'>${tableData.total}</span>个视频</div>", "style": {"lineHeight": "24px"}}]}, {"type": "button-group", "buttons": [{"type": "polaris-middleware", "middleWare": "polaris-button", "id": "download-detail-button", "visibleOn": "${COUNT(tableData.list)}", "props": {"label": "${downloadLoading ? '下载中' : '下载明细'}", "size": "small", "type": "primary", "plain": true, "loading": "${downloadLoading}", "icon": "${downloadLoading ? '' : 'sys-icon-download'}"}, "wrapperClassName": "mr_16", "onEvent": {"click": {"actions": [{"actionType": "polarisHandleEvent", "componentId": "polaris-service-insight-content-realtime", "args": {"event": "downloadDetail"}}]}}}]}]}], "data": {"tableData": "$tableData", "columns": "$columns", "loading": "$tableLoading"}}, {"type": "polaris-middleware", "middleWare": "polaris-pagination", "id": "works-table-pagination", "visibleOn": "tableData.total > 0", "submitOnChange": true, "props": {"background": true, "layout": "prev, pager, next, sizes", "total": "${tableData.total}", "pageSizes": [5, 10, 20, 30, 50, 100], "pageSize": "${pageData.count}", "currentPage": "${pageData.page}", "className": "works-table-pagination", "popperClass": "works-table-pagination__pop"}, "onEvent": {"page-change": {"actions": [{"actionType": "custom", "script": "console.log('$event.data', event.data)"}, {"actionType": "setValue", "componentId": "polaris-service-insight-content-realtime", "expression": "${tableData.total !== 0}", "args": {"value": {"pageData": {"page": "$event.data.value", "count": "$event.data.pageData.count"}}}}]}, "size-change": {"actions": [{"actionType": "setValue", "componentId": "polaris-service-insight-content-realtime", "args": {"value": {"pageData": {"page": 1, "count": "$event.data.value"}}}}]}}}]}]}