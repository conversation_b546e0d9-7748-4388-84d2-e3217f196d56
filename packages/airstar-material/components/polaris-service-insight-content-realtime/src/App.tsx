import React from 'react'
import '../../../utils/global-setup'

// 简化的组件实现，避免依赖polaris-bui-components
const PolarisServiceInsightContentRealtime: React.FC<any> = (props) => {
  console.log('debug polaris-service-insight-content-realtime airstar-material version', props)

  return (
    <div style={{ padding: '20px', border: '1px solid #ddd', borderRadius: '4px' }}>
      <h3>内容洞察实时视频榜组件 (Airstar Material版本)</h3>
      <p>组件正在加载中...</p>
      <pre>{JSON.stringify(props, null, 2)}</pre>
    </div>
  )
}

export default PolarisServiceInsightContentRealtime
