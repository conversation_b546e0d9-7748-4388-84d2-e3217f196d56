import * as path from 'path';
import { type UserConfigExport, defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
// import { createStyleImportPlugin } from 'vite-plugin-style-import';
import { globalViteConfig } from './vite.global.config';

interface ComponentInfo {
  name: string;
  entry: string;
  path: string;
  type: 'base' | 'service' | 'table' | 'filter' | 'complex';
  entryType?: 'react' | 'vue' | 'mixed';
}

/**
 * 创建 UMD 格式的多入口组件构建配置
 * 每个组件生成独立的 UMD 产物，可直接在浏览器中使用
 */
export function createUMDConfig(components: ComponentInfo[]): UserConfigExport {
  // 为每个组件创建入口映射
  const entries: Record<string, string> = {};
  components.forEach(comp => {
    entries[comp.name] = comp.entry;
  });

  return defineConfig({
    // 基础配置
    base: '',

    // 继承全局路径别名
    resolve: globalViteConfig.resolve,

    // 继承全局 CSS 配置
    css: globalViteConfig.css,

    // UMD 构建配置
    build: {
      outDir: 'dist',
      target: 'es2020',
      minify: 'esbuild',

      rollupOptions: {
        // 多个组件入口
        input: entries,

        // 智能外部化函数
        external: (id: string, parentId?: string) => {
                     // 1. 标准外部依赖
           if ([
             'vue', 'react', 'react-dom', '@types/react', '@types/react-dom',
             '@ks/kwai-ui', '@kuaishou/kop-ui', '@ks-operation/kop-ui',
             '@ks-operation/kop-bui-co-table', '@ks-operation/kop-bui-layout-list-page',
             '@ks-operation/kop-bui-file-upload', '@ks-operation/kop-bui-image-upload',
             '@ks/polaris-common-components-vue3',
             'element-plus', 'amis', 'amis-core', 'amis-ui', 'veaury',
             'lodash', 'dayjs', 'moment', 'axios', 'echarts'
           ].some(dep => id === dep || id.startsWith(dep + '/'))) {
             return true;
           }

                     // 2. 内部组件依赖外部化
           if (id.startsWith('@polaris/components/') && id !== parentId) {
             return true;
           }

           // 2.5. @/utils 导入外部化（特殊处理）
           if (id === '@/utils' || id.startsWith('@/utils/')) {
             console.warn(`[External] 外部化@/utils导入: ${id}`);
             return true;
           }

          // 3. 相对路径组件依赖外部化
          if (id.includes('../') && id.includes('polaris-') && parentId) {
            return true;
          }

          // 4. 跨包路径外部化（新增）
          const crossPackagePaths = [
            '/modules/search/search-word-paradigm', '/common/weblog', '/common/dayjs',
            '/common/hook', '/common/util', '/common/format', '/common/hooks',
            '/components/co-table/index.vue', '/components/empty/index.vue', '/assets/img/',
            '/business-components/', '@/business-components/',
            '../../../services/', '../../../utils/', '../../../common/',
            '@/modules/', '@/services/', '@/common/', '@/utils/', '@/components/',
            '/page/author/hook/', '/page/',
            '/utils/blobDownloadFile', '/utils/useHttp', '/utils/mobx-store-manager',
            '/utils/store-manager', '/utils/format', '/utils/const', '/utils/baseJump',
            '/utils/useGlobalData', '/utils/variableProcessor'
          ];
          if (crossPackagePaths.some(path => id.includes(path))) {
            console.warn(`[External] 外部化跨包模块: ${id}`);
            return true;
          }

          // 5. 资源文件外部化（排除Vue组件样式文件）
          const resourcePatterns = [
            '.author-attribution-layer-tag', 'undefined', '.png', '.jpg', '.jpeg', '.svg', '.gif', '.webp',
            '~@/assets/less/', '@ks-operation/polaris-components/lib/styles/',
            '@color-new-brand', '@color-text-primary', '@color-text-regular', '@top-nav-height',
            './assets/img/', '/assets/img/', '~@/assets/'
          ];

          // 检查是否是Vue组件的样式文件，如果是则不外部化
          const isVueComponentStyle = id.includes('.vue?vue&type=style');
          if (!isVueComponentStyle && resourcePatterns.some(pattern => id.includes(pattern))) {
            console.warn(`[External] 外部化资源文件: ${id}`);
            return true;
          }

          // 单独处理独立的样式文件（非Vue组件内的样式）
          const standaloneStylePatterns = ['.less', '.css', '.scss', '.sass'];
          if (!isVueComponentStyle && standaloneStylePatterns.some(pattern => id.endsWith(pattern))) {
            console.warn(`[External] 外部化独立样式文件: ${id}`);
            return true;
          }

          return false;
        },

        output: {
          // UMD 格式输出
          format: 'umd',
          entryFileNames: '[name].umd.js',
          chunkFileNames: 'shared/[name]-[hash].umd.js',
          assetFileNames: 'assets/[name].[ext]',

          // UMD 全局变量映射
          globals: {
            'vue': 'Vue',
            'react': 'React',
            'react-dom': 'ReactDOM',
            '@types/react': 'React',
            '@types/react-dom': 'ReactDOM',
            'lodash': '_',
            'dayjs': 'dayjs',
            'moment': 'moment',
            'axios': 'axios',
            'echarts': 'echarts',
            '@ks/kwai-ui': 'KwaiUI',
            '@kuaishou/kop-ui': 'KopUI',
            '@ks-operation/kop-ui': 'KopUI',
            '@ks-operation/kop-bui-co-table': 'KopBuiCoTable',
            '@ks-operation/kop-bui-layout-list-page': 'KopBuiLayoutListPage',
            '@ks-operation/kop-bui-file-upload': 'KopBuiFileUpload',
            '@ks-operation/kop-bui-image-upload': 'KopBuiImageUpload',
            '@ks/polaris-common-components-vue3': 'PolarisCommonComponents',
            'element-plus': 'ElementPlus',
            'amis': 'Amis',
            'amis-core': 'AmisCore',
            'amis-ui': 'AmisUI',
            'veaury': 'Veaury',
            '@polaris/components': 'PolarisComponents',
            '@polaris/utils': 'PolarisUtils'
          },

          // 确保每个组件是独立的 UMD 模块
          inlineDynamicImports: true,

          // 生成的代码格式
          generatedCode: {
            constBindings: true,
            objectShorthand: true,
            reservedNamesAsProps: false,
            symbols: true
          }
        },

        // 构建警告处理
        onwarn: (warning, warn) => {
          // 忽略样式文件相关的警告
          if (warning.code === 'UNRESOLVED_IMPORT' &&
            (warning.message?.includes('.less') || warning.message?.includes('.css'))) {
            console.warn(`[UMD Build] 忽略样式导入警告: ${warning.message}`);
            return;
          }
          if (warning.code === 'EMPTY_BUNDLE') {
            return;
          }
          warn(warning);
        },
      },

      chunkSizeWarningLimit: 1000,
      cssCodeSplit: false, // UMD 模式下禁用 CSS 代码分割
    },

    plugins: [
      // Vue 3 支持
      vue(),
    ],
  });
}

/**
 * 为单个组件创建独立构建配置（兼容性方法）
 */
export function createSingleUMDConfig(componentInfo: ComponentInfo): UserConfigExport {
  return createUMDConfig([componentInfo]);
}

// 默认导出多入口配置生成函数
export default createUMDConfig;