<template>
    <AmisRender :schema="schema" :props="schemaProps" :selectedItems="selectedItems"/>
</template>

<script setup lang="ts">
import { watch, watchEffect, onBeforeMount, withDefaults } from 'vue';
import { evalExpression, resolveVariableAndFilter } from 'amis-core';
import AmisRender from '../../amis-render/vue_app/amis-render-with-context.vue'
import { KsInput, KsButton } from '@ks/kwai-ui'
import { StoreManager } from '../../../utils/store-manager';
import { MobXStoreManager } from '@polaris/utils/mobx-store-manager';
import { clone, cloneDeep } from 'lodash';

// import batchDialog from './components/batch-dialog.vue';
import { getAllLeaves, getNonOneIdChildrenAtLevel} from '@polaris/utils';


const props = withDefaults(defineProps<{
    filters: any;
    filterData: any;
    initFilterData: any;
    columns: any;
    env: any;
    render: any;
    schemaProps: any;
    storeManager: InstanceType<typeof MobXStoreManager>;
    metadataList: any;
    searchApi: any;
    filterApi: any;
    downloadApi: any;
    schema: any;
}>(), {
    filters: [],
    filterData: {},
    initFilterData: {}
});

console.log('props.storeManager.AmisStore.data', props,props.storeManager.AmisStore.data)
const lastTime = props.storeManager.register('lastTime', 0);
const filterConfig = props.storeManager.register<Record<string, any>>('filterConfig', {});
const filterData = props.storeManager.register<Record<string, any>>('filterData', props.filterData);
const initFilterData = props.storeManager.register<Record<string, any>>('initFilterData', props.initFilterData);
const tabs = props.storeManager.register<{ name: string, value: number, tooltip: string, cnt?: number }[]>('tabs', []);
const currentTab = props.storeManager.register('currentTab', props.storeManager.AmisStore.data['tab'] ?? 0);
const tableLoading = props.storeManager.register('tableLoading', false);
const tableData = props.storeManager.register<{
    total: number,
    list: any[]
}>("tableData", {
    total: 0,
    list: []
});


const columns = props.storeManager.register<any[]>('columns', props.columns ?? []);

const filters = props.storeManager.register<any[]>('filters', props.filters);

// 输入框
const authorKey = props.storeManager.register<string>('authorKey', '');

const pageData = props.storeManager.register("pageData", {
    page: 1,
    count: 10,
});

const selectedItems = props.storeManager.register<any[]>('selectedItems', [])

// 下载状态
const downloadLoading = props.storeManager.register('downloadLoading', false)

// 一般由标注维护，操作框类型
enum DataType {
    text = 1, // 文本 文本配合 enumable 区分是选择框还是输入框
    numInt = 2, // 数字整数 数值输入范围
    numDecimal = 3, // 数字小数 数值输入范围
    date = 4, // 日期
    dateRange = 41, // 日期范围
    tree = 5,
    inputSelect = 6, // 6 下拉单选+输入框
    fansCount = 7, // 7 粉丝量
    remoteSelect = 8, // 8 支持远程搜索下拉框
    radioGroup = 9, // 9 单选radio
    dateTime = 50, // 日期+时间
    radio = 51, // 单选
    composeDate = 52, // 组合日期 周、月、季度
    customDate = 53, // 日期范围、周、月、季度
}

const getFilterData = async () => {
    try {
        const fetchRes = await props.env.fetcher(props.filterApi, props.storeManager.AmisStore.data)
        filterConfig.value = fetchRes.data
        console.log('[debug]filterConfig.value---mlh', filterConfig.value)

    } catch (e) {
        console.log('warn', 'no filter api')
    }
}

// 下载明细
const onDownloadDetail = async () => {
    console.log('[debug] onDownloadDetail 被调用');
    console.log('[debug] downloadLoading.value:', downloadLoading.value);
    console.log('[debug] props.downloadApi:', props.downloadApi);

    if (downloadLoading.value || !props.downloadApi) {
        console.log('[debug] 下载被阻止 - loading:', downloadLoading.value, 'api:', !!props.downloadApi);
        return;
    }

    try {
        downloadLoading.value = true;
        console.log('[debug] 开始下载明细，设置 loading 为 true');

        // 构建下载参数
        const storeData = props.storeManager.AmisStore.data;
        const downloadParams = {
            ...storeData,
            filterData: storeData.filterData,
            authorKey: storeData.authorKey,
            orderBy: storeData.orderBy,
            pageData: storeData.pageData
        };

        console.log('[debug] 下载参数:', downloadParams);
        console.log('[debug] 调用接口:', props.downloadApi);

        // 调用下载接口
        const response = await props.env.fetcher(props.downloadApi, downloadParams, { responseType: 'blob' });

        console.log('[debug] 接口响应:', response);

        // 处理文件下载
        if (response && response.data) {
            const blob = new Blob([response.data], {
                type: response.headers['content-type'] || 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            });

            // 从响应头获取文件名
            let fileName = '内容洞察实时视频榜.xlsx';
            const contentDisposition = response.headers['content-disposition'];
            if (contentDisposition) {
                const fileNameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
                if (fileNameMatch && fileNameMatch[1]) {
                    fileName = decodeURIComponent(fileNameMatch[1].replace(/['"]/g, ''));
                }
            }

            // 创建下载链接
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = fileName;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url);

            console.log('[debug] 下载成功');
        }
    } catch (error) {
        console.error('[debug] 下载失败:', error);
    } finally {
        downloadLoading.value = false;
        console.log('[debug] 下载完成，设置 loading 为 false');
    }
}

// const  getAuthorTagList = (row: any): any[] => {
//     const { sessionLevelStr, firstChannel, secondChannel } =
//         row.item || {};

//     return [
//         sessionLevelStr && {
//             name: sessionLevelStr,
//             className: 'user-layer',
//             toolTip: '打开理由分层',
//             dataValue: sessionLevelStr,
//         },
//         firstChannel && {
//             name: firstChannel,
//             toolTip: '',
//         },
//         secondChannel && {
//             name: secondChannel,
//             toolTip: '',
//         },
//     ].filter(Boolean);
// }


const onSearch = async () => {
    tableData.value.list = []
    tableData.value.total = 0
    tableLoading.value = true

    try {
        const storeData = props.storeManager.AmisStore.data;
        console.log(storeData, 'storeData --- mlh')

        const fetchRes = await props.env.fetcher(props.searchApi, storeData)
        console.log('[debug fetchRes.data --- mlh]', fetchRes.data);
        const {dataList, total} = fetchRes.data
        tableData.value = {
            list: dataList,
            total: total
        }
        console.log(tableData.value, 'tableData.value --- mlh')
        console.log(filterData.value, 'filterData.value --- mlh')
        console.log(storeData.filterData, 'storeData.filterData --- mlh')

    } catch (e) {
        console.log('[debug]tableData error', e);
    } finally {
        tableLoading.value = false;
    }
}

const onReset = () => {
    filterData.value = getInitFilterData();
    onSearch()
}

const filtersConfig = () => {
    if (!Array.isArray(props.filters)) return;

    try {
        const usedList = props.filters.filter(list => {
            const visibleResult = typeof list.visibleOn === 'undefined' || !!list.visibleOn || evalExpression(list.visibleOn, {
                tabActiveName: currentTab.value,
            });
            return visibleResult;
        });

        console.log('[debug]usedList', usedList, filterConfig.value)
        const newFilters = usedList.map(item => {
            if (item.options && typeof item.options === 'string' && item.options.startsWith('$')) {
                const options = resolveVariableAndFilter(
                    item.options,
                    filterConfig.value,
                    '| raw',
                ) || [];
                return {
                    ...item,
                    options,
                };
            }
            return {
                ...item,
            }
        });
        console.log('[debug]newFilters', newFilters)
        filters.value = newFilters;
    } catch (e) {
        console.error('[filter计算错误]', e);
        filters.value = [];
    }
}

watch(() => currentTab.value, filtersConfig, { immediate: true })

function getInitFilterData() {
    const newFilterData: any = {};
    (filters.value ?? []).forEach((val: Record<string, any>) => {
        if (val.dataType != DataType.tree) {
            newFilterData[val.name] = val.multipleChoice
                ? val.items?.map((v: Record<string, any>) => v.value) || []
                : val.items?.[0]?.value || '';
        } else {
            newFilterData[val.name] = val.multipleChoice ? [] : val.items?.[0]?.value || '';
        }
    });
    return newFilterData;
}

watch(() => filterData.value, ()=>{
    pageData.value.page = 1
    onSearch()
})
watch(() => pageData.value, onSearch)
watch(() => currentTab.value, ()=>{
    pageData.value.page = 1
    onSearch()
})

watch(() => authorKey.value, ()=>{
    pageData.value.page = 1
})

onBeforeMount(async () => {
    try {
        await getFilterData().then(() => {
            console.log(filterConfig.value, 'fetchRes')
            filtersConfig();
        })
        onSearch();
    } catch (e) {
        console.log('[debug]error', e);
    }
})

// 注册事件监听器
console.log('[debug] 注册下载事件监听器');
props.storeManager.on('downloadDetail', onDownloadDetail)

props.storeManager.on('search', onSearch)
props.storeManager.on('submit', onSearch)
props.storeManager.on('reset', onReset)
</script>

<style lang="less">
/* 移除有问题的导入，使用内联样式变量 */
@color-brand: #326bfb;

.filter-button {
    color: @color-brand;
    display: flex;
    align-items: center;
    cursor: pointer;
    margin-left: 16px;

    .icon-arrow {
        color: @color-brand !important;
    }
}
</style>
