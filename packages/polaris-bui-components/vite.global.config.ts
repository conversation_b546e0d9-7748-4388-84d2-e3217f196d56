import { defineConfig } from 'vite';
import { resolve } from 'path';
import { fileURLToPath } from 'url';

// ES模块兼容性：获取__dirname
const __filename = fileURLToPath(import.meta.url);
const __dirname = resolve(__filename, '..');

/**
 * 全局Vite配置 - 统一处理CSS样式和依赖
 * 解决打包时CSS样式找不到的问题
 */
export const globalViteConfig = defineConfig({
  // 路径别名配置
  resolve: {
    alias: {
      '@': resolve(__dirname, '.'),
      '@utils': resolve(__dirname, 'utils'),
      '@components': resolve(__dirname, 'components'),
      '@scripts': resolve(__dirname, 'scripts'),
      '@ks/polaris-common-styles': resolve(__dirname, 'node_modules/@ks/polaris-common-styles'),
      '@/assets': resolve(__dirname, 'assets'),
      '~/assets': resolve(__dirname, 'assets'),
      'assets': resolve(__dirname, 'assets'),

      // 目录级别的别名
      '@/services': resolve(__dirname, 'service'),
      '@/common': resolve(__dirname, 'utils'),
      '@/utils': resolve(__dirname, 'utils'),

      // 兼容性别名 - 支持不同的导入方式
      '@/utils/baseJump': resolve(__dirname, 'utils/baseJump'),
      '@/utils/format': resolve(__dirname, 'utils/format'),
      '@/utils/index': resolve(__dirname, 'utils/index'),
      '@/const': resolve(__dirname, 'const/index'),
    },
  },

  // CSS预处理器全局配置
  css: {
    preprocessorOptions: {
      less: {
        additionalData: `
          // 全局样式变量
          @color-new-brand: #ff6b35;
          @color-text-primary: #333333;
          @color-text-secondary: #666666;
          @color-text-regular: #999999;
          @color-background: #ffffff;
          @color-border: #e0e0e0;
          @border-radius: 4px;
          @font-size-base: 14px;
          @line-height-base: 1.5;
          @top-nav-height: 60px;

          // 字体权重mixins
          .font-medium() { font-weight: 500; }
          .font-normal() { font-weight: normal; }
          .font-bold() { font-weight: bold; }

          // 通用mixins
          .underline-text() {
            text-decoration: underline;
            cursor: pointer;
            &:hover { opacity: 0.8; }
          }
          .flex() { display: flex; }
          .flex-center() {
            display: flex;
            align-items: center;
            justify-content: center;
          }
          .text-ellipsis() {
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }

          // 作者标签样式
          .author-attribution-layer-tag {
            display: inline-block;
            padding: 2px 8px;
            background: @color-new-brand;
            color: #fff;
            border-radius: @border-radius;
            font-size: 12px;
          }

          // 占位符样式
          .placeholder-for-missing-resources {
            background: transparent;
            color: inherit;
            font-size: inherit;
          }
          .placeholder-image {
            width: 100%;
            height: 100px;
            background: #f5f5f5;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 14px;
            &::before { content: '图片加载中...'; }
          }
        `,
        javascriptEnabled: true,
        modifyVars: {
          '@color-new-brand': '#ff6b35',
          '@color-text-primary': '#333333',
          '@color-text-secondary': '#666666',
          '@color-text-regular': '#999999',
          '@top-nav-height': '60px'
        }
      }
    }
  },

  // 外部依赖配置 - 统一处理React、Vue、UI库
  build: {
    rollupOptions: {
      external: [
        // React生态
        'react',
        'react-dom',
        'react/jsx-runtime',
        '@types/react',
        '@types/react-dom',

        // Vue生态
        'vue',
        '@vue/runtime-core',
        '@vue/reactivity',

        // UI库
        '@ks/kwai-ui',
        '@ks/kwai-ui/lib/ks-popper',
        '@ks-operation/kop-ui',

        // 工具库
        'lodash',
        'dayjs',
        'moment',
        'axios',

        // Amis相关
        'amis',
        'amis-core',
        'amis-ui',

        // 内部依赖
        '@polaris/utils',
        '@polaris/utils/format',
        '@polaris/components'
      ],

      output: {
        globals: {
          'react': 'React',
          'react-dom': 'ReactDOM',
          'vue': 'Vue',
          '@ks/kwai-ui': 'KwaiUI',
          'lodash': '_',
          'dayjs': 'dayjs',
          'axios': 'axios',
          'amis': 'Amis',
          'amis-core': 'AmisCore',
          'amis-ui': 'AmisUI'
        }
      }
    }
  }
});

// 导出可复用的配置片段
export const sharedPlugins = [];
export const sharedExternal = globalViteConfig.build?.rollupOptions?.external || [];
export const sharedGlobals = {
  'react': 'React',
  'react-dom': 'ReactDOM',
  'vue': 'Vue',
  '@ks/kwai-ui': 'KwaiUI',
  'lodash': '_',
  'dayjs': 'dayjs',
  'axios': 'axios',
  'amis': 'Amis',
  'amis-core': 'AmisCore',
  'amis-ui': 'AmisUI'
};
export const sharedCSSConfig = globalViteConfig.css;
export const sharedAliases = globalViteConfig.resolve?.alias || {};