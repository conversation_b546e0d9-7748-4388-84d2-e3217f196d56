<!-- eslint-disable max-len -->
<template>
    <div class="works-table" v-loading="globalData.loading">
        <div class="table-top flex jc_space_between">
            <div class="table-top-left">
                <div
                    v-if="headerToolbars.length"
                    class="header flex jc_space_between"
                >
                    <div
                        class="flex ai_center"
                        v-for="(toolbar, index) in headerToolbars"
                        :key="index"
                    >
                        <div
                            :key="getUniqueRowKey(toolbar)"
                            v-if="toolbar.framework === 'react'"
                            :style="toolbar.layout"
                            class="header-item"
                        >
                            <AmisRender
                                v-if="toolbar.framework === 'react'"
                                :key="getUniqueRowKey(toolbar)"
                                :data="toolbar.props"
                                :schema="toolbar"
                                :props="props"
                            />
                        </div>
                        <!-- Vue组件 -->
                        <div
                            :key="`vue-${getUniqueRowKey(toolbar)}`"
                            v-else
                            :style="toolbar.layout"
                            :class="['header-item', toolbar.className]"
                        >
                            <!-- {{ toolbar.props }} -->
                            <component
                                :is="componentMap[toolbar.type]"
                                v-bind="{
                                    // ...$attrs,
                                    ...toolbar.props,
                                }"
                                :key="getUniqueRowKey(toolbar)"
                                @dispatch-event="
                                    (eventName, event) =>
                                        emit('dispatchEvent', eventName, event)
                                "
                            />
                        </div>
                    </div>
                </div>
            </div>
            <div class="table-top-right">
                <ks-button size="small" @click="openCustomColumnDialog">
                    自定义列
                </ks-button>
                <ks-button
                    size="small"
                    @click="
                        emit('dispatchEvent', 'polarisHandleEvent', {
                            event: 'download',
                        })
                    "
                    icon="iconfont icon-xiazai"
                    :loading="downloadLoading"
                    :disabled="downloadDisable"
                >
                    下载明细
                </ks-button>
                <ks-button
                    size="small"
                    v-if="isBatchUpdate === 1 && isAllowedOperation"
                    @click="exitBatchOperation"
                >
                    退出批量操作
                </ks-button>
                <ks-button
                    size="small"
                    plain
                    @click="batchUpdateLabels"
                    v-if="isAllowedOperation && globalData.dynamicTagFilterData && globalData.dynamicTagFilterData.length > 0"
                    :disabled="isBatchUpdateDisabled"
                >
                    {{ isBatchUpdate === 1 ? "执行批量操作" : "批量更新标签" }}
                </ks-button>
            </div>
        </div>
        <template
            v-if="
                !!globalData.filterData?.operationPlanId &&
                globalData.tableData.list &&
                globalData.tableData.list.length > 0
            "
        >
            <amis-table
                v-if="globalData.tableData.list"
                :key="`${globalData.filterData?.boostType}-table`"
                :row-key="globalData.tableData.list.authorId"
                class="works-table-body"
                :hideSelection="isBatchUpdate === -1"
                :data="globalData.tableData.list"
                :columns="usedColumns"
                :header-cell-style="{
                    background: '#F9FAFB',
                    color: '#898A8C',
                    'font-weight': 400,
                }"
                ref="childComponentRef"
                @select="handleChange"
                @select-all="handleSelectAll"
            >
                <template
                    v-for="(slot, index) in [
                        ...slotSchema,
                        ...dynamicColumnAndSlots.dynamicTagSlots,
                        ...dynamicColumnAndSlots.dynamicHeaderTagSlots
                    ]"
                    #[slot.name]="slotProps"
                >
                    <AmisRender
                        v-if="slot.framework === 'react'"
                        :component-key="`react-${getUniqueRowKey({ row: slotProps?.row, slot })}`"
                        :schema="slot"
                        :data="slot.getScopedData(slotProps)"
                        :props="props"
                    />
                   
                    <component
                        v-else
                        :key="`vue-${getUniqueRowKey({ row: slotProps?.row, slot })}`"
                        :is="componentMap[slot.type]"
                        v-bind="{
                            // ...$attrs,
                            ...slot.getScopedData(slotProps),
                            dynamicTagFilterData: globalData.dynamicTagFilterData
                        }"
                        @openUpdateLabelsDialog="openUpdateLabelsDialog"
                        @goToAuthorOpenReasonDetail="goToAuthorOpenReasonDetail"
                        @openLoggingDrawer="openLoggingDrawer"
                    />
                </template>
            </amis-table>
            <ks-pagination
                class="works-table-pagination"
                popper-class="works-table-pagination__pop"
                background
                v-if="
                    globalData.tableData.list &&
                    globalData.tableData.list.length > 0 &&
                    globalData.tableData.total > 10
                "
                layout="prev, pager, next, sizes"
                :total="globalData.tableData.total"
                :page-sizes="[10, 20, 30, 50, 100]"
                :page-size="globalData.pageData?.pageSize"
                :current-page="globalData.pageData?.page"
                @size-change="handleSizeChange"
                @prev-click="handlePageChange"
                @next-click="handlePageChange"
                @current-change="handlePageChange"
            >
            </ks-pagination>
        </template>
        <empty
            v-else
            :description="
                !globalData.filterData?.operationPlanId ||
                globalData.filterData?.operationPlanId?.length === 0
                    ? '请选择运营计划'
                    : '暂无数据'
            "
        ></empty>
        <author-label-dialog
            v-if="openUpdateLabelsDialogVisible"
            v-model:show="openUpdateLabelsDialogVisible"
            :dynamicTags="globalData.dynamicTagFilterData"
            ref="batchAssignRef"
            :env="env"
            :selectedIds="selectedIds"
            :selectedRow="selectedRow"
            :isMultipleUpdate="selectedRow === null && selectedIds.length > 0"
            @rest-select="selectedRow = null"
            @init-batch-operation="exitBatchOperation"
            @dispatch-event="
                (eventName, event) => emit('dispatchEvent', eventName, event)
            "
        >
        </author-label-dialog>
        <author-custom-column
            @dispatch-event="
                (eventName, event) => emit('dispatchEvent', eventName, event)
            "
            v-if="openCustomColumnDialogVisible"
            v-model:show="openCustomColumnDialogVisible"
            :visibleColumnList="globalData.visibleColumnList"
            ref="batchAssignRef"
            :dynamicTags="globalData.dynamicTagFilterData"
        >
        </author-custom-column>
        <author-log-drawer
            :id="logDialog.id"
            v-model:visible="logDialog.visible"
            :env="env"
        ></author-log-drawer>
    </div>
</template>

<script lang="ts" setup>
import { ref, computed, useAttrs } from "vue";
import {
    KsButton,
    // KsTable,
    KsTableColumn,
    KsPagination,
    KsMessage,
    KsCheckbox,
    Loading,
} from "@ks/kwai-ui";
import AmisTable from '../components/table.vue';
import Rank from '../../base/rank.vue'
import AmisRender from '../../amis-render/vue_app/index.vue'
import AuthorData from '../../polairs-author-info/vue_app/index.vue'
import ValueDiff from '../../base/value-diff.vue'
import OperationOrderOperation from '../components/operation-order-operation.vue'
import CommonHeaderTooltip from '../../base/common-header-tooltip.vue'
import RankWithSelector from '../../base/rank-with-selector/index.vue'
import { type TypeListItem } from "./types";
import { goToAuthorOpenReasonDetail } from '../../../utils/index';

import { useTable, getScopedData } from "../../../utils/useTable";
import { StoreManager } from "../../../utils/store-manager";
import {
    formatNumberWithSuffix,
    formatNumThousand,
} from "../../../utils/format";
import AuthorLevelChart from "./author-level-chart/author-level-chart.vue";
import AuthorLabelDialog from "./author-label-dialog/author-label-dialog.vue";
import AuthorCustomColumn from "./author-custom-column/author-custom-column.vue";
import AuthorLogDrawer from "./author-log-drawer/author-log-drawer.vue";
import RadioGroup from '../components/radio-group.vue'
import Empty from '../../polaris-complex-filter-v1/vue_app/base/empty/index.vue'
import TagTooltip from './tag-tooltip/index.vue'
import CoText from '../../base/co-text/index.vue';
// import TableTopLeft from "@polaris/components/base/table-top-left/index.vue";
const vLoading = Loading.directive;

const logDialog = ref({
    id: -1,
    visible: false,
});

const isBatchUpdate = ref(-1); // 是否批量更新
const selectedIds = ref([]); // 存放选中的项id
const isAllSelected = ref(false); // 是否全部选中
const downloadLoading = ref(false);
const downloadDisable = ref(false);
const childComponentRef = ref(null);
// 升序/降序枚举
enum ESortType {
    ascending = 1, // 升序
    descending = 2, // 降序
}

// 排序字段
enum ESortKey {
    intention30d = 10, // 30日归因量
}
const props = withDefaults(
    defineProps<{
        spanMethod?: string;
        columns?: Record<string, any>[];
        rowKey?: string;
        amisRender?: any;
        id: any;
        env: any;
        storeManager: StoreManager;
        metadataList: any;
        headerToolbar: any[];
    }>(),
    {
        spanMethod: "",
        columns: () => [],
    },
);

const attrs = useAttrs();

const componentMap = {
    rank: Rank,
    'common-header-tooltip': CommonHeaderTooltip,
    'author-data': AuthorData,
    'value-diff': ValueDiff,
    'author-level-chart': AuthorLevelChart,
    'operation-order-operation': OperationOrderOperation,
    'radio-group': RadioGroup,
    'rank-with-selector': RankWithSelector,
    'tag-tooltip': TagTooltip,
    'co-text': CoText,
}

const { headerToolbars, getUniqueRowKey, slotSchema, globalData } = useTable(
    props,
    attrs,
    (data) => {
        // noop
        // console.log("[debug]收到最新的tableData", data);
    },
);

const dynamicColumnAndSlots = computed(() => {
    if (!globalData.value.dynamicTagFilterData?.length) {
        return { dynamicTagColumns: [], dynamicTagSlots: [], dynamicHeaderTagSlots: [] };
    }

    // 提取共用的基础配置和工具函数
    const baseHeaderProps = {
        tooltip: '计划内最新执行的标注工单结果',
        effect: 'dark',
        placement: 'top',
        underline: true,
        titleClassName: 'column-underline'
    };

    const createScopedDataGetter = (config) => {
        return (rowProps) => getScopedData(config, rowProps, globalData.value);
    };

    // 生成动态列配置
    const dynamicTagColumns = globalData.value.dynamicTagFilterData.map(item => ({
        headerSlot: `header-tooltip-${item.tagName}`,
        key: item.tagId,
        attrs: {
            'min-width': `${item.tagName.length * 14 + 5 * 14}px`,
        },
        prop: item.tagName,
        slot: `dynamic-tag-${item.tagName}`,
    }));

    // 生成动态列槽位配置
    const dynamicTagSlots = globalData.value.dynamicTagFilterData.map(item => {
        const baseConfig = {
            name: `dynamic-tag-${item.tagName}`,
            scope: 'row',
            props: {
                row: '$row',
                tagId: item.tagId
            }
        };

        return {
            ...baseConfig,
            framework: 'vue',
            type: 'tag-tooltip',
            getScopedData: createScopedDataGetter({
                ...baseConfig,
                framework: 'vue',
                type: 'tag-tooltip'
            })
        };

        // if (item.tagName.includes('运营跟进动作')) {
        //     return {
        //         ...baseConfig,
        //         framework: 'vue',
        //         type: 'tag-tooltip',
        //         getScopedData: createScopedDataGetter({
        //             ...baseConfig,
        //             framework: 'vue',
        //             type: 'tag-tooltip'
        //         })
        //     };
        // }

        // return {
        //     ...baseConfig,
        //     framework: 'react',
        //     type: 'tpl',
        //     tpl: `\${row.${item.tagId}}`,
        //     getScopedData: createScopedDataGetter({
        //         ...baseConfig,
        //         framework: 'react',
        //         type: 'tpl'
        //     })
        // };
    });

    // 生成动态表头槽位配置
    const dynamicHeaderTagSlots = globalData.value.dynamicTagFilterData.map(item => {
        const config = {
            name: `header-tooltip-${item.tagName}`,
            framework: 'vue',
            type: 'common-header-tooltip',
            props: {
                title: `标签-${item.tagName}`,
                ...baseHeaderProps
            }
        };

        return {
            ...config,
            getScopedData: createScopedDataGetter(config)
        };
    });

    return {
        dynamicTagColumns,
        dynamicTagSlots,
        dynamicHeaderTagSlots
    };
});

function splitObjectsByKeys(array: any[]) {
    // 定义要查找的键
    const specificKeys = ['photoNums', 'liveNums', 'growthFansNums', 'operation'];

    // 初始化两个结果数组
    const matchedObjects = [];
    const otherObjects = [];

    // 遍历原始数组
    for (const item of array) {
        // 检查对象是否包含任何特定键
        const hasSpecificKey = specificKeys.includes(item.key);

        // 根据条件将对象放入相应的数组
        if (hasSpecificKey) {
            matchedObjects.push(item);
        } else {
            otherObjects.push(item);
        }
    }

    return [otherObjects, matchedObjects]
    // return {
    //     matchedObjects,   // 包含指定key的对象数组
    //     otherObjects      // 不包含指定key的对象数组
    // };
}

const usedColumns = computed(() => {

    // console.log('[debug]dynamicColumns', dynamicColumns)
    const columns = globalData.value?.columns || props.columns || []
    // columns.push(...dynamicColumns)
    const visibleColumnList = globalData.value.visibleColumnList;
    // 根据是否显式为false判断显隐
    const [beforeColumn, afterColumn] = splitObjectsByKeys(columns)
    const combinedColumns = [...beforeColumn, ...dynamicColumnAndSlots.value.dynamicTagColumns, ...afterColumn]
    const visibleColumns = combinedColumns.filter((column: { key: string | number; }) => visibleColumnList[column.key] !== false)
    return visibleColumns
    // if (isBatchUpdate.value !== -1) {
    //     return visibleColumns
    // } else {
    //     // 非多选情况，过滤prop / attr为selection的属性
    //     const selectedColumns = visibleColumns.filter((column: { prop: string; attr: { type: string; }; }) => column.prop !== 'selection' && column.attr?.type !== 'selection')
    //     return selectedColumns
    // }
})

const emit = defineEmits<{
    (
        e: "dispatchEvent",
        eventName: string,
        event:
            | { path: string | number; value: any, submit: boolean; }
            | { path: string | number; value: any, submit: boolean; }[]
            | { event: string; data?: any },
    ): void;
}>();
// pageData
const handlePageChange = (page: number) => {
    emit("dispatchEvent", "polarisSetValue", {
        path: "pageData",
        value: {
            ...globalData.value.pageData,
            page,
        },
        submit: true,
    });
};

const handleSizeChange = (size: number) => {
    emit("dispatchEvent", "polarisSetValue", {
        path: "pageData",
        value: {
            ...globalData.value.pageData,
            pageSize: size,
            page: 1,
        },
        submit: true,
    });
};

// 下载按钮
// const handleDownload = (page: number) => {
//     // const params = getSharedParams();
//     try {
//         downloadLoading.value = true;
//         emit('dispatchEvent', 'polarisHandleEvent', {
//             event: 'download',
//         })
//         // Message.success(data.res);
//     } finally {
//         downloadLoading.value = false;
//     }
// };
const isAllowedOperation = computed(() => {
    return globalData.value?.tableData?.list?.some(item => item.allowedOperationFlag === true);
});

// 新增计算属性，用于判断批量操作按钮是否需要禁用
const isBatchUpdateDisabled = computed(() => {
    // 如果不是在批量操作模式，则按钮不禁用
    if (isBatchUpdate.value !== 1) return false;

    // 如果在批量操作模式下没有选中任何项，则禁用按钮
    return !selectedIds.value.length;
});

// 批量操作
// 批量更新标签
const batchUpdateLabels = () => {
    if (isBatchUpdate.value === 1) {
        openUpdateLabelsDialogVisible.value = true;
        selectedRow.value = null; // 确保在批量模式下清空单行选择
        selectedIds.value = globalData.value.tableData.list
            .filter((item: { allowedOperationFlag: boolean; isSelected: boolean; }) => item.allowedOperationFlag && item.isSelected)
            .map((item: { interveneOrderId: any; }) => item.interveneOrderId);
    } else {
        isBatchUpdate.value = 1;
    }
};
// 退出批量操作
const exitBatchOperation = () => {
    isBatchUpdate.value = -1;
    selectedIds.value = [];
    childComponentRef.value?.ksTableRef?.clearSelection()
    // handleSelectAll([]);
    // if (globalData.value && globalData.value.tableData && globalData.value.tableData.list) {
    //     const freshData = JSON.parse(JSON.stringify(globalData.value.tableData.list));
    //     freshData.forEach((item: any) => {
    //         item.isSelected = false;
    //     });
    //     globalData.value.tableData.list = freshData;
    // }
    isAllSelected.value = false;
};
//全选本页
// const selectAllOnPage = (e: any) => {
//     if (e) {
//         selectedIds.value = globalData.value.tableData.list.map(
//             (item: { authorId: any; }) => item.authorId,
//         );
//         globalData.value.tableData.list.forEach((item: { isSelected: boolean; }) => {
//             item.isSelected = true;
//         });
//     } else {
//         selectedIds.value = [];
//         globalData.value.tableData.list.forEach((item: { isSelected: boolean; }) => {
//             item.isSelected = false;
//         });
//     }
// };
// 处理全选事件
const handleSelectAll = (selection: any[]) => {
    selectedIds.value = selection.map((item: { authorId: any; }) => item.authorId);

    // 更新列表中所有项的选中状态
    globalData.value.tableData.list.forEach((item) => {
        item.isSelected = selection.length > 0; // 如果selection为空，则取消全选
    });
    console.log(selection,'selection mlh',globalData.value.tableData.list)

    isAllSelected.value = selection.length > 0;
};
const selectedRow = ref<any>()
// 单选
const handleChange = (_: any, row: any) => {
    // const { authorId } = row
    selectedIds.value = _.map((item: { authorId: any; }) => item.authorId)
    globalData.value.tableData.list.forEach((item) => {
        return item.isSelected = selectedIds.value.includes(item.authorId);
    });
    // selectedIds.value = _.map((item: { authorId: any; }) => item.authorId)
    // console.log('[debug]handleChange received value', _, row)
    // if (selectedIds.value.includes(authorId)) {
    //     selectedIds.value = globalData.value.tableData.list.map(
    //         (item) => item.isSelected,
    //     );
    //     globalData.value.tableData.list.forEach((item) => {
    //         if (item.authorId == authorId) {
    //             item.isSelected = false;
    //         }
    //     });
    //     isAllSelected.value = false;
    // } else {
    //     globalData.value.tableData.list.forEach((item) => {
    //         if (item.authorId == authorId) {
    //             item.isSelected = true;
    //         }
    //     });
    //     selectedIds.value.push(authorId);
    //     if (
    //         selectedIds.value.length === globalData.value.tableData.list.length
    //     ) {
    //         isAllSelected.value = true;
    //     }
    // }
};

// 打开日志
const openLoggingDrawer = (data: { interveneOrderId: number }) => {
    logDialog.value.id = data.interveneOrderId;
    logDialog.value.visible = true;
};
// 更新标签
const openUpdateLabelsDialogVisible = ref(false);
const openUpdateLabelsDialog = (row: any) => {
    selectedRow.value = row;
    openUpdateLabelsDialogVisible.value = true;
};
// 自定义列
const openCustomColumnDialogVisible = ref(false);
const openCustomColumnDialog = (data: any) => {
    openCustomColumnDialogVisible.value = true;
};

const handleSortChange = (sort: {
    sortField: string | number;
    sortType: string;
}) => {
    emit("dispatchEvent", "polarisSetValue", {
        path: "sortValue",
        value: sort,
        submit: true,
    });
};
</script>
<style lang="less" scoped>
@import "../../../../../apps/polaris-common-styles/base.less";
@media (min-width: 1793px) {
    .works-detail {
        max-width: 14vw !important;
        width: auto !important;
    }

    .works-title {
        max-width: 14vw !important;
        width: auto !important;
    }

    .works-author-name {
        width: auto;
        max-width: 80% !important;
    }

    .gap-line-left {
        margin-left: 1vw !important;
        margin-right: 1vw !important;
    }

    .gap-line {
        margin-left: 1vw !important;
        margin-right: 1vw !important;
    }
}

@media (min-width: 1793px) {
    .works-video-id {
        padding-left: 2vw !important;
    }
}

@media (min-width: 2000px) {
    .works-video-id {
        padding-left: 2.5vw !important;
    }
}

@media (min-width: 2300px) {
    .works-video-id {
        padding-left: 3.2vw !important;
    }
}

.works-table {
    .table-top {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .header {
            margin-top: 16px;
            margin-bottom: 10px;
        }

        .table-top-left {
            display: flex;
            align-items: center;

            .vertical-line {
                display: inline-block;
                margin-left: 8px;
                margin-right: 8px;
                border-left: 1px solid #ebedf0;
                height: 10px;
            }
        }

        .table-top-right {
            display: flex;
            justify-content: center;
            align-items: center;

            .ks-button {
                margin-left: 16px;
            }
        }
    }

    .works-table-body {
        margin-bottom: 24px;

        :deep(.ks-table__empty-text) {
            color: #898a8c;
            font-family: "PingFang SC";
            font-size: 14px;
            font-style: normal;
            font-weight: 300;
            line-height: 22px;
        }

        :deep(.ks-table__body-wrapper) {
            .ks-table__expanded-cell {
                z-index: 11;
            }
        }

        .column-index {
            &-first {
                color: #fe3666;
                text-align: center;
                font-family: Impact;
                font-size: 16px;
                font-style: normal;
                font-weight: 400;
                line-height: 24px;
                text-align: center;
            }

            &-second {
                color: #ff6f00;
                text-align: center;
                font-family: Impact;
                font-size: 16px;
                font-style: normal;
                font-weight: 400;
                line-height: 24px;
                text-align: center;
            }

            &-third {
                color: #ffa600;
                text-align: center;
                font-family: Impact;
                font-size: 16px;
                font-style: normal;
                font-weight: 400;
                line-height: 24px;
            }

            &-other {
                color: #bbbdbf;
                text-align: center;
                font-family: Impact;
                font-size: 16px;
                font-style: normal;
                font-weight: 400;
                line-height: 24px;
                text-align: center;
            }
        }

        .column-header {
            display: flex;
            align-items: center;
        }

        :deep(.ks-table__expanded-cell) {
            &:hover {
                background-color: #fafafa !important;
            }

            background-color: #fafafa;
        }

        :deep(.table-operation) {
            .cell {
                .ks-button + .ks-button {
                    margin-left: 0;
                }
            }
        }

        :deep(th) {
            padding: 8px 0;
        }

        :deep(td) {
            padding: 8px 0;
        }
    }

    .works-table-pagination {
        justify-content: flex-end;

        :deep(.ks-select .ks-input) {
            margin-right: 0;
        }
    }
}

.insight-author-photo-count {
    font-weight: 500;
    color: #326bfb;
    cursor: pointer;
}

.insight-author-photo-count__is-zero {
    color: #252626;
    cursor: auto;
}

.dashline_vertical {
    margin-left: 8px;
    margin-right: 8px;
    border-left: 1px solid #d5d6d9;
    height: 12px;
}

.empty-tips {
    margin-top: 100px;
}

.increase-value {
    color: #fa4e3e;
}

.decrease-value {
    color: #30c453;
}
:deep(.ks-radio-tag) {
    transform: scale(0.7);
    margin-left: 0 !important;
    margin-right: 0 !important;
}

:deep(.ks-radio-tag__inner) {
    display: flex;
    justify-content: center;
    align-items: center;
    line-height: 1 !important;
    font-size: 18px;
    background-color: #f5f7fa !important;
    padding: 2px 4px !important;
    border-radius: 5px !important;
    border: none;
}

:deep(.radio-group) {
    display: flex;
    flex-wrap: wrap;
}
</style>
<style lang="less">
.column-underline {
    border-bottom: 1px dashed #898a8c;
}

.works-table-pagination__pop {
    position: static;
}

.advantage-attr-source {
    white-space: pre-line;
    width: 100%;
}

.speator-line {
    position: relative;

    &::after {
        position: absolute;
        content: "";
        display: block;
        height: 12px;
        width: 1px;
        background: #d5d6d9;
        top: 50%;
        right: -15px;
        transform: translateY(-50%);
    }
}
</style>
