/*通用的css*/
@import "../polaris-common-styles/mixins.less";

.none {
    display: none !important;
}
.bg_base {
    background-color: @background-base;
}
.tc_danger {
    color: @color-danger;
}
.tc_light {
    color: @color-text-light;
}
.tc_primary {
    color: @color-text-primary;
}
.tc_regular {
    color: @color-text-regular;
}
.tc_success {
    color: @color-success;
}
.tc_danger {
    color: @color-danger;
}
.wb_br {
    word-break: break-word;
}
.wb_ba {
    word-break: break-all;
}
.ws_nowrap {
    white-space: nowrap;
}
.ws_pre_wrap {
    white-space: pre-wrap;
}
img {
    object-fit: cover;
}
.disabled {
    color: #bbbdbf;
    pointer-events: none;
}
.tf {
    font-family: din1451alt;
}
@font-face {
    font-family: din1451alt;
    src: url(./font/din.ttf);
}
.scale_50p {
    transform: scale(0.5);
}
.scale_83p {
    transform: scale(0.83);
}
.title-wrap {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
}
.page-title__h1 {
    font-family: 'PingFang SC';
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: 28px !important /* 140% */;
    background: #fff;
    padding: 16px 0 12px;
    color: @color-text-primary;
}
.page-title__h2 {
    color: @color-text-primary;
    /* CN 中文/Title2 */
    font-family: "PingFang SC";
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 24px /* 150% */;
}
.page-wrap {
    padding: 16px 0 24px;
    font-size: 14px;
    box-sizing: border-box;
    position: relative;
}
.common-border-color {
    border-color: @background-base !important;
}
.common-bg-color {
    background: @background-base;
}
.amis-polaris-pagination {
    position: static;
}
