import path from 'path';
// import vue from "@vitejs/plugin-vue";
import { type UserConfigExport, loadEnv, defineConfig } from 'vite';
import zeus from '@ks/zeus-vite-plugin';
import zeusDev from '@ks/zeus-vite-dev-plugin';
import UnoCSS from 'unocss/vite';
import unocssConfig from './src/common/config/unocss.config';
import { createStyleImportPlugin } from 'vite-plugin-style-import';
import AutoImport from 'unplugin-auto-import/vite';
// import Components from 'unplugin-vue-components/vite';
// @ts-ignore
import veauryVitePlugins from 'veaury/vite/index.js';
import packageConfig from './package.json';
import { KwaiUiResolver } from './lib/kwai-ui-resolver';

// https://vitejs.dev/config/
const isProd = 'production' === process.env.NODE_ENV;

export default defineConfig(({ mode }) => {
    process.env = { ...process.env, ...loadEnv(mode, process.cwd()) };
    const config: UserConfigExport = {
        base: process.env.VITE_FRAME
            ? '/'
            : !isProd
                ? `https://s2-12537.kwimgs.com/kos/nlav12537/micro/`
                : `https://s2-12537.kwimgs.com/kos/nlav12537/micro/`,
        build: {
            assetsDir: `${packageConfig.name}/static`,
            rollupOptions: {
                input: path.join(__dirname, 'src/main.ts'),
                preserveEntrySignatures: 'exports-only',
            },
            chunkSizeWarningLimit: 1000,
        },
        css: {
            preprocessorOptions: {
                less: {
                    modifyVars: {
                        hack: `true; @import (reference) "${path.resolve('src/assets/less/index.less')}";`,
                    },
                    javascriptEnabled: true,
                },
            },
        },
        plugins: [
            // vue(),
            veauryVitePlugins({
                type: 'vue',
                vueJsxOptions: {
                    babelPlugins: [
                        [
                            '@babel/plugin-proposal-decorators',
                            {
                                legacy: true,
                            },
                        ],
                    ],
                },
                reactOptions: {
                    babel: {
                        plugins: [
                            [
                                '@babel/plugin-proposal-decorators',
                                { legacy: true },
                            ],
                        ],
                    },
                    fastRefresh: false, // 暂时先关掉热更新吧，开启热更新一直有报错
                },
            }),
            zeus({
                path: `${packageConfig.name}`,
            }),
            createStyleImportPlugin({
                libs: [
                    {
                        libraryName: '@ks/kwai-ui',
                        esModule: true,
                        ensureStyleFile: true,
                        resolveStyle: (name): string => {
                            if (name === 'locale') {
                                return '';
                            }
                            const realName = name.replace(/^(ks-)/, "");
                            if (realName === 'tooltip') {
                                // 防止和主应用中的kop-ui样式互相有影响
                                return `@ks-operation/kop-ui/lib/theme-new-era/${realName}.css`;
                            }
                            return `@ks/kwai-ui/lib/theme-new-era/${realName}.css`;
                        },
                    },
                    {
                        libraryName: '@ks-operation/kop-bui-vue3',
                        esModule: true,
                        ensureStyleFile: true,
                        resolveStyle: (): string => {
                            return `@ks-operation/kop-bui-vue3/dist/style.css`;
                        },
                    },
                ],
            }),
            UnoCSS(unocssConfig()),
            AutoImport({
                imports: ['vue', 'vue-router'],
                eslintrc: {
                    enabled: true,
                },
                dts: './auto-imports.d.ts',
            }),
        ],
        server: {
            host: 'local.polaris.staging.kuaishou.com',
            port: 1089,
            // https://kdev.corp.kuaishou.com/web/api-mock/proxy?id=190  引入子应用的default代理
            proxy: {
                '/rest': {
                    target: 'https://polaris.corp.kuaishou.com',
                    changeOrigin: true,
                    headers: {
                        origin: 'https://polaris.corp.kuaishou.com',
                        cookie: "apdid=bd8b3cad-2c08-45ac-9a95-85179838cd9cd053f007ef480ff1adf9125d79fea1b4:1749538567:1; weblogger_did=web_519283053E626521; hdige2wqwoino=yKrHy7zsC7maX5akTkeJCCkTFEjAdaHF1b616437; _did=web_1158464197517DC4; _ga=GA1.1.1244435677.1749642658; _ga_F6CM1VE30P=GS2.1.s1749642658$o1$g0$t1749642660$j58$l0$h0; did=web_fe246c0f75ac33d64ae0f2bdc6edcfb292a6; userId=4836075549; kwpsecproductname=PCLive; kwfv1=PnGU+9+Y8008S+nH0U+0mjPf8fP08f+98f+nLlwnrIP9P9G98YPf8jPBQSweS0+nr9G0mD8B+fP/L98/qlPe4f8eqFPBLEG0mjPfcl8nzjG9QDPnclG0cM+erEPArU8ePE+/DEG0ZU+/zjG9HlP9LIGAYYGfp0+nzj8ePMG/4SP/r=; Airstar-userInfo-token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySW5mbyI6eyJuYW1lIjoic29uZ2ppYXFpMDMiLCJkaXNwbGF5TmFtZSI6IuWui-WutueQqiIsIm1haWwiOiJzb25namlhcWkwM0BrdWFpc2hvdS5jb20iLCJhdmF0YXIiOiIiLCJkZXBhcnRtZW50Ijoi5Li756uZ5oqA5pyv6YOoIiwibmFtZVpoIjoi5a6L5a6255CqIn0sImlhdCI6MTc1MjYzNTcwN30.luugNAcQTqMks3969Y5zofRrV3dUsel-rdRHolNVsqg; polaris-master-type=stable; polaris_tk_prod=ottWf0iq17BUbu2iX2MsLU3air0Kx8kEBKvYXdUjSYuLZnL3UAeUdSWhUQJEBAfhZHJ74dbItsztyUziXMQBt8G/GrOOHSMSl+MHmpaSw/+WD7qLGZ7Vq65RJx4Mg5iY; ksCorpDeviceid=f8b5a2bc-b268-49db-8311-e47760a55fce; ehid=4Hp3buL_3GHdTTz7mxOsFUAb2AMwPP_LZlyq_; accessproxy_session=2b05f675-d9e9-40ca-a46a-db76e0926e0d"
                    },
                },
                '/getKconf': {
                    target:
                        process.env.VITE_APP_DEV_PROXY ||
                        'https://koasproxy.corp.kuaishou.com/proxy/712dc000',
                    changeOrigin: true,
                },
                '/accessproxy_statics/wm.js': {
                    target: 'https://polaris.staging.kuaishou.com/',
                    changeOrigin: true,
                },
            },
        },
        resolve: {
            alias: [
                {
                    find: '@react',
                    replacement: path.join(__dirname, 'react_app'),
                },
                {
                    find: '@polaris',
                    replacement: path.join(__dirname, '../../', 'packages/polaris-bui-components'),
                },
                {
                    find: '@airstar',
                    replacement: path.join(__dirname, '../../', 'packages/airstar-material'),
                },
                {
                    find: '@',
                    replacement: path.join(__dirname, 'src'),
                },
                {
                    find: /^~@/,
                    replacement: path.join(__dirname, 'src'),
                },
            ],
        },
    };
    if (process.env.VITE_FRAME) {
        config.plugins?.push(
            zeusDev({
                html: 'https://polaris.staging.kuaishou.com/',
                htmlRequestConfig: {
                    gzip: true,
                },
                jsEntry: '/src/main.ts',
                appAssetMapJsonPath: `/${packageConfig.name}/asset-map.json`,
                proxy: {
                    '^/static': 'https://polaris.staging.kuaishou.com/',
                    '^/([^/]+)/static': 'https://polaris.staging.kuaishou.com/',
                    '^/(.+)/asset-map.json$': {
                        target: 'https://polaris.staging.kuaishou.com/',
                        changeOrigin: true,
                    },
                },
            }),
        );
    }
    return config;
});
